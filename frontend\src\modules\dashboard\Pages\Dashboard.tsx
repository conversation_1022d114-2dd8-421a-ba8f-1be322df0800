import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Container,
  Avatar,
  AppBar,
  Toolbar,
  Paper,
  Chip,
} from '@mui/material';
import Grid from '@mui/material/Grid';
import {
  Business,
  People,
  Assignment,
  CalendarToday,
  BarChart,
  Settings,
  Logout,
  TrendingUp,
  AccessTime,
  CheckCircle,
} from '@mui/icons-material';

import { Button } from '../../../components/common/Button';

interface DashboardProps {
  onLogout: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  const modules = [
    {
      icon: People,
      name: 'Employee Management',
      description: 'Manage employee records and profiles',
      color: '#1976d2',
    },
    {
      icon: Assignment,
      name: 'Project Management',
      description: 'Track projects and milestones',
      color: '#388e3c',
    },
    {
      icon: CalendarToday,
      name: 'Leave Management',
      description: 'Handle leave requests and approvals',
      color: '#f57c00',
    },
    {
      icon: <PERSON><PERSON><PERSON>,
      name: 'Reports & Analytics',
      description: 'View detailed reports and insights',
      color: '#7b1fa2',
    },
    {
      icon: Settings,
      name: 'System Settings',
      description: 'Configure system preferences',
      color: '#616161',
    },
  ];

  const stats = [
    { label: 'Total Employees', value: '150', color: 'primary', icon: People },
    { label: 'Active Projects', value: '23', color: 'success', icon: TrendingUp },
    { label: 'Pending Requests', value: '7', color: 'warning', icon: AccessTime },
    { label: 'System Uptime', value: '95%', color: 'info', icon: CheckCircle },
  ];

  const recentActivities = [
    { action: 'New employee John Doe added to Marketing Department', time: '2 hours ago' },
    { action: 'Project "Website Redesign" milestone completed', time: '4 hours ago' },
    { action: 'Leave request from Sarah Wilson approved', time: '1 day ago' },
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      {/* Header */}
      <AppBar
        position="static"
        elevation={2}
        sx={{
          background: 'linear-gradient(135deg, #29b6f6 0%, #0277bd 100%)',
        }}
      >
        <Toolbar>
          <Avatar sx={{ mr: 2, bgcolor: 'rgba(255,255,255,0.2)' }}>
            <Business />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" component="div">
              WorkFlow Pro Dashboard
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Welcome back, Admin
            </Typography>
          </Box>
          <Button
            color="inherit"
            startIcon={<Logout />}
            onClick={onLogout}
            sx={{
              bgcolor: 'rgba(255,255,255,0.1)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' },
            }}
          >
            Logout
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Quick Stats */}
        <Typography variant="h5" sx={{ mb: 3, color: 'text.primary' }}>
          Quick Stats
        </Typography>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card elevation={3} sx={{ height: '100%', borderRadius: 2 }}>
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Avatar
                      sx={{
                        width: 56,
                        height: 56,
                        mx: 'auto',
                        mb: 2,
                        bgcolor: `${stat.color}.main`,
                      }}
                    >
                      <Icon sx={{ fontSize: 28 }} />
                    </Avatar>
                    <Typography variant="h4" sx={{ mb: 1, color: `${stat.color}.main` }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        {/* System Modules */}
        <Typography variant="h5" sx={{ mb: 3, color: 'text.primary' }}>
          System Modules
        </Typography>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          {modules.map((module, index) => {
            const Icon = module.icon;
            return (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card
                  elevation={3}
                  sx={{
                    height: '100%',
                    borderRadius: 2,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      elevation: 8,
                      transform: 'translateY(-4px)',
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          width: 48,
                          height: 48,
                          bgcolor: module.color,
                          mr: 2,
                        }}
                      >
                        <Icon />
                      </Avatar>
                      <Typography variant="h6" component="h3">
                        {module.name}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {module.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        {/* Recent Activity */}
        <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h6" sx={{ mb: 3 }}>
            Recent Activity
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {recentActivities.map((activity, index) => (
              <Box
                key={index}
                sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
              >
                <Typography variant="body2">{activity.action}</Typography>
                <Chip label={activity.time} size="small" variant="outlined" />
              </Box>
            ))}
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};