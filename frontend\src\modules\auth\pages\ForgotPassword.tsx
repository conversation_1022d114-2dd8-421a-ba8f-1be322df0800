import React from 'react';
import { Box, Container, Paper, Avatar, Typography } from '@mui/material';
import Grid from '@mui/material/Grid';
import { Email, Shield, ArrowBack } from '@mui/icons-material';

import { Button } from '../../../components/common/Button';
import { FormCard } from '../../../components/common/FormCard';
import { CompanyLogo } from '../../../components/layout/CompanyLogo';

interface ForgotPasswordProps {
  onBack: () => void;
  onEmailReset: () => void;
  onAdminReset: () => void;
}

export const ForgotPassword: React.FC<ForgotPasswordProps> = ({
  onBack,
  onEmailReset,
  onAdminReset,
}) => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #e0f2fe 0%, #ffffff 50%, #e0f2fe 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
      }}
    >
      <Container maxWidth="md">
        <CompanyLogo />

        <FormCard
          title="Forgot Password"
          subtitle="Choose how you'd like to reset your password"
          maxWidth={600}
        >
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  borderRadius: 2,
                  border: '2px solid transparent',
                  '&:hover': {
                    elevation: 4,
                    bgcolor: '#e3f2fd',
                    transform: 'translateY(-2px)',
                    borderColor: 'primary.main',
                  },
                }}
                onClick={onEmailReset}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar 
                    sx={{ 
                      bgcolor: '#e3f2fd', 
                      color: 'primary.main',
                      width: 56,
                      height: 56,
                    }}
                  >
                    <Email sx={{ fontSize: 28 }} />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ mb: 0.5 }}>
                      Reset via Email OTP
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      We'll send a reset link to your email address
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  borderRadius: 2,
                  border: '2px solid transparent',
                  '&:hover': {
                    elevation: 4,
                    bgcolor: '#fff3e0',
                    transform: 'translateY(-2px)',
                    borderColor: 'warning.main',
                  },
                }}
                onClick={onAdminReset}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar 
                    sx={{ 
                      bgcolor: '#fff3e0', 
                      color: 'warning.main',
                      width: 56,
                      height: 56,
                    }}
                  >
                    <Shield sx={{ fontSize: 28 }} />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ mb: 0.5 }}>
                      Request Admin Reset
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Submit a request to your system administrator
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>

          <Button
            variant="outlined"
            fullWidth
            size="large"
            startIcon={<ArrowBack />}
            onClick={onBack}
            sx={{
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                borderColor: 'primary.dark',
                bgcolor: '#e3f2fd',
              },
            }}
          >
            Back to Login
          </Button>
        </FormCard>
      </Container>
    </Box>
  );
};